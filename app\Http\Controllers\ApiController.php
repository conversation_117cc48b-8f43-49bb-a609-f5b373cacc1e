<?php

/**
 * 外挂控制器类
 * @desc 外挂控制器类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 202/02/23
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Controllers;

use App\Models\StarRocks\AntiChectHitLog;
use App\Models\StarRocks\AntiChectHitLogDetail;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Services\Plugin\ListService;
use App\Services\Plugin\SceneListService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{
    /**
     * 项目映射
     *
     * @var array
     */
    const PROJECT_MAP = [
        107 => 6,   /* 长安SDK的项目ID是107，对应效能后台的项目ID是6 */
        116 => 13,   /* 长安SDK的项目ID是116，对应效能后台的项目ID是13 */
    ];

    /**
     * 检测用户或角色是否命中外挂
     *
     * @param Request $request
     * @return array
     */
    public function check(Request $request): array
    {
        // 验证参数
        $params = $request->all();
        if (!$request->has('project_id')) {
            return $this->fail(1003, 'project_id参数错误');
        }
        // 判断项目ID是否有做映射
        if (!isset(self::PROJECT_MAP[$params['project_id']])) {
            return $this->fail(1003, 'project_id参数错误，没有映射');
        }
        // 判断账号ID或角色ID是否存在
        if (!$request->has('account_id') && !$request->has('role_id')) {
            return $this->fail(1003, 'account_id或role_id参数错误');
        }
        // 判断是否有角色ID，有角色ID则要判断服务器ID是否存在
        if ($request->has('role_id') && !isset($params['server_id'])) {
            return $this->fail(1003, 'server_id参数错误');
        }
        // 获取请求数据
        $limit = $params['limit'] ?? 10;    /* 获取限制条数，默认10条 */
        $limit = min(200, $limit);          /* 限制最大条数为200 */
        $projectId = self::PROJECT_MAP[$params['project_id']];   /* 获取项目ID */
        $roleId = $params['role_id'] ?? null;   /* 获取角色ID，默认 null */
        $serverId = $params['server_id'] ?? null;   /* 获取服务器ID，默认 null */
        $accountId = $params['account_id'] ?? null;   /* 获取账号ID，默认 null */

        try {
            $mainTable = AntiChectInit::TABLE_NAME;
            $hitTable = AntiChectHitLog::TABLE_NAME;
            $dataTable = AntiChectUploadDataPrimary::TABLE_NAME;
            // 获取数仓数据
            $list = AntiChectInit::query()
                ->selectRaw("{$hitTable}.*, {$mainTable}.stream_date as create_date, {$dataTable}.report_interval")
                ->join($dataTable, "{$mainTable}.session_id", '=', "{$dataTable}.session_id")
                ->join($hitTable, "{$mainTable}.session_id", '=', "{$hitTable}.session_id")
                ->where("{$mainTable}.extra_app_id", $projectId)
                ->when($accountId, function ($query, $value) use ($dataTable) {
                    return $query->whereRaw("get_json_string({$dataTable}.game_info, 'account_id') = '{$value}'");
                })
                ->when($roleId, function ($query, $value) use ($dataTable) {
                    return $query->whereRaw("get_json_string({$dataTable}.game_info, 'role_id') = '{$value}'");
                })
                ->when($serverId, function ($query, $value) use ($dataTable) {
                    return $query->whereRaw("get_json_string({$dataTable}.game_info, 'server_id') = '{$value}'");
                })
                ->latest("{$hitTable}.stream_date")
                ->limit($limit)
                ->getFromSR();
            // 判断数据是否为空
            if (empty($list)) {
                return $this->success([
                    'status' => false,
                    'logs' => []
                ]);
            }
            // 获取命中外挂记录的次数
            $hitList = AntiChectHitLogDetail::query()
                ->selectRaw('session_id, COUNT(*) AS count, min(stream_date) AS begin_time')
                ->whereIn('session_id', array_column($list, 'session_id'))
                ->groupBy('session_id')
                ->getFromSR();
            // 把session_id作为索引，组成一个数组
            $hitList = array_column($hitList, null, 'session_id');
            // 格式化数据
            return $this->success([
                'status' => true,
                'logs' => array_map(function ($item) use ($hitList) {
                    $portInfo = json_decode($item['port_info'], true);
                    $socketInfo = json_decode($item['socket_info'], true);
                    $clickInfo = json_decode($item['click_info'], true);
                    return [
                        'begin_time' => $hitList[$item['session_id']]['begin_time'] ?? '',  /* 第一次开挂时间 */
                        'check_time' => $item['stream_date'],
                        'is_hit_port' => count($portInfo) > 0,
                        'is_hit_socket' => count($socketInfo) > 0,
                        'is_hit_click' => $clickInfo['ratio'] >= 80,
                        'plugin_time' => $this->getPluginDuration($item['stream_date'], $item['create_date'], intval($hitList[$item['session_id']]['count'] ?? 0) * intval($item['report_interval'])),
                    ];
                }, $list)
            ]);
        } catch (Exception $e) {
            Log::error('API检测用户或角色是否命中外挂接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(1005, '系统异常');
        }
    }

    /**
     * 获取导出列表详情数据
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request): array
    {
        // 验证参数
        $params = $request->all();
        if (!$request->has('project_id')) {
            return $this->fail(1003, 'project_id参数错误');
        }
        // 判断项目ID是否有做映射
        if (!isset(self::PROJECT_MAP[$params['project_id']])) {
            return $this->fail(1003, 'project_id参数错误，没有映射');
        }
        // 判断开始和结束时间参数
        if (!$request->has('start_date') || !$request->has('end_date')) {
            return $this->fail(1003, 'start_date和end_date参数错误');
        }
        // 判断风险等级参数
        if (!$request->has('risk_level')) {
            return $this->fail(1003, 'risk_level参数错误');
        }
        // 获取请求数据
        $limit = $params['limit'] ?? 10;    /* 获取限制条数，默认10条 */
        $limit = min(200, $limit);          /* 限制最大条数为200 */
        $page = $params['page'] ?? 1;          /* 页码 */
        $projectId = self::PROJECT_MAP[$params['project_id']];   /* 获取项目ID */
        // 获取数据
        try {
            // 实例化服务类
            $service = new ListService([
                'extra_app_id' => $projectId,
                'start_date' => date('Y-m-d H:i:s', $params['start_date']),
                'end_date' => date('Y-m-d H:i:s', $params['end_date']),
                'page' => $page,
                'pre_page' => $limit,
                'risk_level' => [$params['risk_level']]
            ]);
            // 返回数据
            return $this->success([
                'total' => $service->getCount(),
                'list' => $service->getExportList(),
            ]);
        } catch (Exception $e) {
            Log::error('API获取导出列表详情数据接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(1005, '系统异常');
        }
    }

    /**
     * 获取场景信息列表
     *
     * @param Request $request
     * @return array
     */
    public function scene(Request $request): array
    {
        // 验证参数
        $params = $request->all();
        if (!$request->has('project_id')) {
            return $this->fail(1003, 'project_id参数错误');
        }
        // 判断账号ID列表
        if (!$request->has('account_list')) {
            return $this->fail(1003, 'account_list参数错误');
        }
        // 判断项目ID是否有做映射
        if (!isset(self::PROJECT_MAP[$params['project_id']])) {
            return $this->fail(1003, 'project_id参数错误，没有映射');
        }
        // 判断开始和结束时间参数
        if (!$request->has('start_date') || !$request->has('end_date')) {
            return $this->fail(1003, 'start_date和end_date参数错误');
        }
        // 获取请求数据
        $limit = 100000;    /* 获取限制条数，默认10万条 */
        $page = $params['page'] ?? 1;          /* 页码 */
        $projectId = self::PROJECT_MAP[$params['project_id']];   /* 获取项目ID */
        // 获取数据
        try {
            $service = new SceneListService([
                'extra_app_id' => $projectId,
                'start_date' => date('Y-m-d H:i:s', $params['start_date']),
                'end_date' => date('Y-m-d H:i:s', $params['end_date']),
                'page' => $page,
                'pre_page' => $limit,
                'account_list' => explode(',', $params['account_list']),
                'screen_id' => $params['screen_id'] ?? null,
            ]);
            // 返回数据
            return $this->success([
                'total' => $service->getCount(),
                'list' => $service->getList(),
            ]);
        } catch (Exception $e) {
            Log::error('API获取场景信息列表接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(1005, '系统异常');
        }
    }

    /**
     * 获取外挂时长
     *
     * @param string $updateDate
     * @param string $createDate
     * @param int $hitDuration
     * @return string
     */
    private function getPluginDuration($updateDate, $createDate, $hitDuration)
    {
        // 获取报告时长，单位：秒，最后上报时间减去初始化时间
        $reportDuration = strtotime($updateDate) - strtotime($createDate);
        // 外挂时长，判断外挂时长是否大于报告时长，大于则取报告时长，否则取外挂时长
        return min($hitDuration, $reportDuration);
    }

    /**
     * 成功返回
     *
     * @param array $data
     * @param string $message
     * @return array
     */
    protected function success(array $data = [], string $message = 'ok'): array
    {
        return [
            'code' => 0,
            'msg' => $message,
            'data' => $data,
        ];
    }

    /**
     * 失败返回
     *
     * @param int $code
     * @param string $message
     * @param array $data
     * @return array
     */
    protected function fail(int $code, string $message = 'fail', array $data = []): array
    {
        return [
            'code' => $code,
            'msg' => $message,
            'data' => $data,
        ];
    }
}
