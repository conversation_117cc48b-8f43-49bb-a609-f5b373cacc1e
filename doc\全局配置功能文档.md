# 全局配置功能文档

## 功能概述

全局配置功能允许每个应用(developer_app_id)存储和管理任意格式的JSON配置数据。该功能提供了灵活的配置管理能力，支持各种业务场景的配置需求。

## 功能特性

- **一应用一配置**：每个应用只有一条全局配置记录
- **JSON格式存储**：支持任意结构的JSON配置数据
- **简洁接口**：只提供获取和编辑两个核心接口
- **配置监控**：集成配置变更监控，记录所有配置修改操作
- **默认值支持**：应用首次访问时返回默认空配置

## 数据库设计

### 表结构：global_config

```sql
CREATE TABLE `global_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `developer_app_id` int unsigned NOT NULL DEFAULT '0' COMMENT '研发效能APP项目id',
  `config_data` json NOT NULL COMMENT '配置数据(JSON格式)',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_developer_app_id` (`developer_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局配置表';
```

### 字段说明

- `id`: 主键ID，自增
- `developer_app_id`: 研发效能APP项目ID，唯一索引
- `config_data`: JSON格式的配置数据
- `created_at`: 创建时间
- `updated_at`: 更新时间

## API接口文档

### 1. 获取全局配置信息

**接口地址**: `GET /global-config/getConfigInfo`

**请求参数**:
```json
{
  "developer_app_id": 1
}
```

**参数说明**:
- `developer_app_id`: 必填，整数，应用ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "developer_app_id": 1,
    "config_data": {
      "url": "https://www.baidu.com",
      "status": 1,
      "timeout": 30,
      "custom_settings": {
        "feature_a": true,
        "feature_b": false
      }
    },
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

### 2. 编辑全局配置

**接口地址**: `POST /global-config/edit`

**请求参数**:
```json
{
  "developer_app_id": 1,
  "config_data": {
    "url": "https://www.baidu.com",
    "status": 1,
    "timeout": 30,
    "custom_settings": {
      "feature_a": true,
      "feature_b": false,
      "threshold": 100
    }
  }
}
```

**参数说明**:
- `developer_app_id`: 必填，整数，应用ID
- `config_data`: 必填，JSON对象，配置数据

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": []
}
```

## 配置数据示例

### 基础配置示例
```json
{
  "url": "https://api.example.com",
  "status": 1,
  "timeout": 30
}
```

### 复杂配置示例
```json
{
  "api_settings": {
    "base_url": "https://api.example.com",
    "timeout": 30,
    "retry_count": 3
  },
  "feature_flags": {
    "enable_cache": true,
    "enable_logging": false,
    "max_connections": 100
  },
  "business_config": {
    "payment_methods": ["alipay", "wechat", "bank"],
    "default_currency": "CNY",
    "tax_rate": 0.06
  }
}
```

## 使用说明

1. **首次使用**：应用首次调用获取配置接口时，会返回默认的空配置
2. **配置更新**：通过编辑接口可以新增或更新配置，系统会自动判断是新增还是更新操作
3. **配置监控**：所有配置变更都会被记录和监控，便于追踪配置变化
4. **数据格式**：配置数据必须是有效的JSON格式，支持嵌套结构

## 注意事项

- 每个应用只能有一条全局配置记录
- 配置数据必须是有效的JSON格式
- 所有配置变更都会被监控和记录
- 建议在配置中使用有意义的键名，便于维护和理解
