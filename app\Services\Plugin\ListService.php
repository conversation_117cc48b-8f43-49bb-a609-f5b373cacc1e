<?php

/**
 * 外挂检测数据列表类
 * @desc 外挂检测数据列表类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/12/21
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use App\Models\StarRocks\AntiChectHitLog;
use App\Models\StarRocks\AntiChectHitLogDetail;
use App\Models\StarRocks\AntiChectInit;
use App\Models\StarRocks\AntiChectUploadDataPrimary;
use App\Models\StarRocks\AntiChectUploadDataPrimaryV2;
use Illuminate\Support\Carbon;

class ListService extends BaseService
{
    /**
     * 获取数据
     *
     * @return array
     */
    public function getData()
    {
        return ['list' => $this->getList(), 'total' => $this->getCount()];
    }

    /**
     * 获取数据总数
     *
     * @return int
     */
    public function getCount()
    {
        //获取总记录数
        $total = $this->getBuilder()->selectRaw('count(*) as total')->firstFromSR();
        // 返回结果
        return intval($total['total'] ?? 0);
    }

    /**
     * 获取列表内容
     *
     * @return array
     */
    public function getList()
    {
        $builder = $this->getBuilder();

        //需要获取的数据库字段
        $mainTable = $builder->getModel()->getTable();
        $dataTable = AntiChectUploadDataPrimaryV2::TABLE_NAME;
        $selectRaw = <<<RAW
{$mainTable}.session_id,
{$mainTable}.stream_date,
{$mainTable}.server_dev_str,
{$mainTable}.app_package_name,
{$mainTable}.app_version,
{$mainTable}.is_emulator,
{$mainTable}.resolution,
{$mainTable}.screen_density_dpi,
{$mainTable}.is_root,
{$mainTable}.is_cloudPhone,
{$mainTable}.os_type,
{$mainTable}.device_brand,
{$dataTable}.stream_date as update_date,
{$dataTable}.application_info,
{$dataTable}.write_info,
{$dataTable}.is_write,
{$dataTable}.game_info,
{$dataTable}.risk_level,
{$dataTable}.report_interval,
{$dataTable}.hitbug_explain_desc
RAW;
        $page = ($this->params['page'] ?? 1) - 1;
        $limit = $this->params['pre_page'] ?? 10;
        // 获取数据
        $list = $builder
            ->selectRaw($selectRaw)
            ->offset($page < 0 ? 0 : $page * $limit)
            ->limit($limit)
            ->orderBy($mainTable . '.' . ($this->params['sort_field'] ?? 'stream_date'), $this->params['sort_type'] ?? 'desc')
            ->getFromSR();
        //处理数据
        return $this->handleList($list);
    }

    /**
     * 设置分页
     *
     * @param int $page
     * @return ListService
     */
    public function setPage($page)
    {
        $this->params['page'] = $page;
        return $this;
    }

    /**
     * 处理数据
     *
     * @param array $list
     * @return array
     */
    private function handleList(array $list)
    {
        // 判断list是否为空
        if (empty($list)) {
            return [];
        }
        // 获取list第一个元素和最后一个元素的时间
        $firstStreamDate = $list[0]['stream_date'];
        $lastStreamDate = $list[count($list) - 1]['stream_date'];
        // 判断第一个元素的时间大于最有一个元素的时间则调换
        if ($firstStreamDate > $lastStreamDate) {
            $temp = $firstStreamDate;
            $firstStreamDate = $lastStreamDate;
            $lastStreamDate = $temp;
        }
        //获取列表中是否有命中风险的报告
        $highReports = AntiChectHitLog::query()
            ->selectRaw('session_id, risk_level, port_info')
            ->whereIn('session_id', array_column($list, 'session_id'))
            ->getFromSR();
        $highReports = array_column($highReports, null, 'session_id');

        foreach ($list as $key => $value) {
            //设置参数的默认值
            $list[$key]['account_id'] = '';
            $list[$key]['role_id'] = '';
            $list[$key]['role_name'] = '';
            $list[$key]['risk_level'] = 0;
            $list[$key]['server_id'] = '';
            $list[$key]['server_name'] = '';

            //获取游戏相关信息
            $gameInfo = json_decode($value['game_info'], true);
            if ($gameInfo) {
                $list[$key]['account_id'] = $gameInfo['account_id'];
                $list[$key]['role_id'] = $gameInfo['role_id'];
                $list[$key]['role_name'] = $gameInfo['role_name'];
                $list[$key]['server_id'] = $gameInfo['server_id'];
                $list[$key]['server_name'] = $gameInfo['server_name'];
            }

            //检测风险等级
            if (isset($highReports[$value['session_id']])) {
                $list[$key]['risk_level'] = static::RISK_LEVEL_MAP[intval($highReports[$value['session_id']]['risk_level'])];
            } else {
                $list[$key]['risk_level'] = (new CheckRiskService([
                    'write_info' => $value['write_info'],
                    'resolution' => $value['resolution'],
                    'screen_density_dpi' => $value['screen_density_dpi'],
                    'is_root' => $value['is_root'],
                    'risk_level' => $value['risk_level'],
                    'is_emulator' => $value['is_emulator'],
                ]))->getLevel();
            }

            // 获取风险标签
            $list[$key]['risk_tag'] = $this->getRiskTag([
                'application_info' => $value['application_info'],
                'is_emulator' => $value["is_emulator"],
                'device_brand' => $value["device_brand"],
                'is_root' => $value['is_root'],
                'is_write' => $value['is_write'],
                'write_info' => $value['write_info'],
                'screen_density_dpi' => $value["screen_density_dpi"],
                'resolution' => $value["resolution"],
                'port_info' => $value['os_type'] == AntiChectInit::PC ? ($highReports[$value['session_id']]['port_info'] ?? []) : [],
            ]);

            //移除不需要的参数
            $unsetKeys = ['game_info', 'application_info', 'write_info', 'resolution', 'screen_density_dpi', 'is_root', 'is_cloudPhone'];
            foreach ($unsetKeys as $unsetKey) {
                unset($list[$key][$unsetKey]);
            }
        }
        return $list;
    }

    /**
     * 获取导出数据列表
     *
     * @return array
     */
    public function getExportList()
    {
        // 获取数据
        $list = $this->getList();
        // 判断list是否为空
        if (empty($list)) {
            return [];
        }
        // 获取list第一个元素和最后一个元素的时间
        $firstStreamDate = $list[0]['stream_date'];
        $lastStreamDate = $list[count($list) - 1]['stream_date'];
        // 判断第一个元素的时间大于最有一个元素的时间则调换
        if ($firstStreamDate > $lastStreamDate) {
            $temp = $firstStreamDate;
            $firstStreamDate = $lastStreamDate;
            $lastStreamDate = $temp;
        }
        // 获取命中外挂记录的次数
        $hitList = AntiChectHitLogDetail::query()
            ->selectRaw('session_id, COUNT(*) AS count')
            ->whereIn('session_id', array_column($list, 'session_id'))
            ->where('stream_date', '>=', $firstStreamDate)
            ->where('stream_date', '<=', Carbon::parse($lastStreamDate)->addDay()->toDateTimeString())
            ->groupBy('session_id')
            ->getFromSR();
        // 把session_id作为索引，count作为值，组成一个数组
        $hitCount = array_column($hitList, 'count', 'session_id');
        // 存放处理好的数据
        $newList = [];
        // 写入数据
        foreach ($list as $item) {
            $newList[] = [
                'session_id' => $item['session_id'],
                'stream_date' => $item['stream_date'],
                'server_dev_str' => $item['server_dev_str'],
                'os_type' => AntiChectInit::PLATFORM[$item['os_type']],
                'account_id' => $item['account_id'],
                'server_id' => $item['server_id'],
                'server_name' => $item['server_name'],
                'role_id' => $item['role_id'],
                'role_name' => $item['role_name'],
                'risk_tag' => $item['risk_tag'],
                'risk_level' => AntiChectInit::RISK_LEVEL[$item['risk_level']],
                'phone_type' => AntiChectInit::PHONE_TYPE[$item['is_emulator']],
                'app_package_name' => $item['app_package_name'],
                'app_version' => $item['app_version'],
                'plugin_time' => $this->getPluginDuration($item['update_date'], $item['stream_date'], intval($hitCount[$item['session_id']] ?? 0) * intval($item['report_interval'])),
                'hitbug_explain_desc' => $item['hitbug_explain_desc'],
            ];
        }
        // 返回数据
        return $newList;
    }

    /**
     * 获取外挂时长
     *
     * @param string $updateDate
     * @param string $createDate
     * @param int $hitDuration
     * @return string
     */
    private function getPluginDuration($updateDate, $createDate, $hitDuration)
    {
        // 获取报告时长，单位：秒，最后上报时间减去初始化时间
        $reportDuration = strtotime($updateDate) - strtotime($createDate);
        // 外挂时长，判断外挂时长是否大于报告时长，大于则取报告时长，否则取外挂时长
        $duration = min($hitDuration, $reportDuration);
        // 转换单位，显示小时、分钟、秒
        $hour = floor($duration / 3600);
        $minute = floor(($duration % 3600) / 60);
        $second = $duration % 60;
        // 分钟为空时，只显示秒
        if ($hour == 0 && $minute == 0) {
            return "{$second}秒";
        }
        // 小时为0时，显示分钟、秒
        if ($hour == 0 && $minute > 0) {
            return "{$minute}分钟{$second}秒";
        }
        // 显示小时、分钟、秒
        return "{$hour}小时{$minute}分钟{$second}秒";
    }
}
