<?php

/**
 * 录屏记录校验类
 * @desc 录屏记录校验类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2023/09/11
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Http\Validation;

/**
 * @method static PluginValidation build()
 */
class PluginValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): PluginValidation
    {
        $this->rules['extra_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 报告ID
     *
     * @return $this
     */
    public function sessionId(): PluginValidation
    {
        $this->rules['session_id'] = 'required|string';
        return $this;
    }

    /**
     * 开始时间
     *
     * @return $this
     */
    public function startDate(): PluginValidation
    {
        $this->rules['start_date'] = 'date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 结束时间
     *
     * @return $this
     */
    public function endDate(): PluginValidation
    {
        $this->rules['end_date'] = 'date_format:Y-m-d H:i:s';
        return $this;
    }

    /**
     * 平台
     *
     * @return $this
     */
    public function osType(): PluginValidation
    {
        $this->rules['os_type'] = 'array';
        $this->rules['os_type.*'] = 'required';
        return $this;
    }

    /**
     * 账号ID
     *
     * @return $this
     */
    public function accountId(): PluginValidation
    {
        $this->rules['account_id'] = 'array';
        $this->rules['account_id.*'] = 'required';
        return $this;
    }

    /**
     * 设备ID
     *
     * @return $this
     */
    public function serverDevStr(): PluginValidation
    {
        $this->rules['server_dev_str'] = 'array';
        $this->rules['server_dev_str.*'] = 'required';
        return $this;
    }

    /**
     * IP地址
     *
     * @return $this
     */
    public function ip(): PluginValidation
    {
        $this->rules['ip_address'] = 'array';
        $this->rules['ip_address.*'] = 'required';
        return $this;
    }

    /**
     * 角色ID
     *
     * @return $this
     */
    public function serverId(): PluginValidation
    {
        $this->rules['server_id'] = 'array';
        $this->rules['server_id.*'] = 'required';
        return $this;
    }

    /**
     * 角色ID
     *
     * @return $this
     */
    public function roleId(): PluginValidation
    {
        $this->rules['role_id'] = 'array';
        $this->rules['role_id.*'] = 'required';
        return $this;
    }

    /**
     * 角色名称
     *
     * @return $this
     */
    public function roleName(): PluginValidation
    {
        $this->rules['role_name'] = 'string';
        return $this;
    }

    /**
     * 包名
     *
     * @return $this
     */
    public function appPackageName(): PluginValidation
    {
        $this->rules['app_package_name'] = 'array';
        $this->rules['app_package_name.*'] = 'required';
        return $this;
    }

    /**
     * 插件版本
     *
     * @return $this
     */
    public function sdkVer(): PluginValidation
    {
        $this->rules['sdk_ver'] = 'array';
        $this->rules['sdk_ver.*'] = 'required';
        return $this;
    }

    /**
     * 应用版本
     *
     * @return $this
     */
    public function appVersion(): PluginValidation
    {
        $this->rules['app_version'] = 'array';
        $this->rules['app_version.*'] = 'required';
        return $this;
    }

    /**
     * 风险等级
     *
     * @return $this
     */
    public function riskLevel(): PluginValidation
    {
        $this->rules['risk_level'] = 'array';
        $this->rules['risk_level.*'] = 'required';
        return $this;
    }

    /**
     * 执行动作
     *
     * @return $this
     */
    public function action(): PluginValidation
    {
        $this->rules['action'] = 'array';
        $this->rules['action.*'] = 'required';
        return $this;
    }

    /**
     * 排序字段
     *
     * @return $this
     */
    public function sortField(): PluginValidation
    {
        $this->rules['sort_field'] = 'string';
        return $this;
    }

    /**
     * 排序类型
     *
     * @return $this
     */
    public function sortType(): PluginValidation
    {
        $this->rules['sort_type'] = 'string';
        return $this;
    }

    /**
     * 分页
     *
     * @return $this
     */
    public function page(): PluginValidation
    {
        $this->rules['page'] = 'integer';
        return $this;
    }

    /**
     * 分页大小
     *
     * @return $this
     */
    public function prePage(): PluginValidation
    {
        $this->rules['pre_page'] = 'integer';
        return $this;
    }

    /**
     * 任务ID
     *
     * @return $this
     */
    public function taskId(): PluginValidation
    {
        $this->rules['task_id'] = 'required|string';
        return $this;
    }
}
