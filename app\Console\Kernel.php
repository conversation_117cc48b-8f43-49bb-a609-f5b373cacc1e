<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        //每分钟执行
        // $schedule->command("check:plugin:status")->runInBackground()->everyMinute();

        // 检查白名单有效状态的定时脚本
        $schedule->command("check:whitelist:status")->runInBackground()->daily()->appendOutputTo(storage_path('logs/CheckWhitelistStatus.log'));
        // 检查黑名单有效状态的定时脚本
        $schedule->command("check:blacklist:status")->runInBackground()->daily()->appendOutputTo(storage_path('logs/CheckBlacklistStatus.log'));
        // 每分钟执行
        $schedule->command("sync:primary:data")->everyMinute()->runInBackground();
        // $schedule->command("sync:primary:pack")->everyMinute()->runInBackground();
        //只在正式服执行的脚本
        if (config('app.env') === 'production') {
            //每天10点，执行
            $schedule->command("monitor:data")->withoutOverlapping()->dailyAt('10:01')->runInBackground();
            //每天10点，执行
            // $schedule->command("monitor:hit:data")->withoutOverlapping()->dailyAt('10:01')->runInBackground();
            // 检查新增外挂应用定时脚本
            $schedule->command("check:plugin:app:devices 6")->runInBackground()->dailyAt('09:00')->appendOutputTo(storage_path('logs/CheckPluginAppDevices.log'));
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
